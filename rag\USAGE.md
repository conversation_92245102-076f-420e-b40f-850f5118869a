# 向量化工具使用指南

基于 `default_config.py` 配置的 OpenAI Embeddings 向量化工具。

## 🚀 快速开始

### 1. 确保配置正确

检查 `default_config.py` 中的 embedding 配置：

```python
# default_config.py 中的相关配置
"embedding_llm": "BAAI/bge-m3",
"embedding_url": "https://api.siliconflow.cn/v1", 
"embedding_api_key": "sk-yasxyjkrvevjwtbvxsxyaulmxwtibwqoidvjfposfnvvuhhk"
```

### 2. 基本使用

```python
from rag.simple_vectorizer import SimpleVectorizer

# 自动从 default_config.py 获取配置
vectorizer = SimpleVectorizer()

# 单个文本向量化
text = "人工智能正在改变世界"
vector = vectorizer.vectorize_text(text)
print(f"向量维度: {len(vector)}")  # 输出: 向量维度: 1024

# 批量文档向量化
documents = [
    "机器学习是AI的核心",
    "深度学习推动了AI发展"
]
vectors = vectorizer.vectorize_documents(documents)
print(f"向量数量: {len(vectors)}")  # 输出: 向量数量: 2

# 通用方法（自动判断类型）
single_vector = vectorizer.vectorize("单个文本")
batch_vectors = vectorizer.vectorize(["文本1", "文本2"])
```

### 3. 运行示例

```bash
# 测试配置
uv run python rag/vectorizer_config.py

# 运行完整示例
uv run python rag/vectorizer_example.py

# 测试简单向量化器
uv run python rag/simple_vectorizer.py
```

## 📋 当前配置信息

根据你的 `default_config.py`：

- **模型**: BAAI/bge-m3
- **API URL**: https://api.siliconflow.cn/v1
- **向量维度**: 1024
- **批处理大小**: 16（针对BGE模型优化）

## 🔧 高级功能

### 使用增强版向量化器

```python
from rag.enhanced_vectorizer import EnhancedVectorizer
from rag.vectorizer_config import get_default_config

# 获取配置
config = get_default_config()
vectorizer = EnhancedVectorizer(config)

# 批量处理（带进度条）
documents = ["文档1", "文档2", "文档3", "文档4"]
vectors = vectorizer.vectorize_documents_batch(
    documents,
    batch_size=2,
    show_progress=True,
    max_retries=3
)

# 带回调函数的处理
def process_batch(batch_index, batch_vectors):
    print(f"处理完第{batch_index + 1}批")

vectors = vectorizer.vectorize_with_callback(
    documents,
    callback=process_batch
)
```

### 自定义配置

```python
from rag.vectorizer_config import VectorizerConfig

# 手动创建配置
config = VectorizerConfig.from_dict({
    "api_key": "your-api-key",
    "model": "text-embedding-3-small",
    "base_url": "https://api.openai.com/v1",
    "batch_size": 100
})

vectorizer = SimpleVectorizer(config=config)
```

## ✅ 测试结果

运行测试显示所有功能正常：

```
✓ 使用default_config.py配置初始化成功
✓ 模型: BAAI/bge-m3
✓ API URL: https://api.siliconflow.cn/v1
✓ 向量维度: 1024
✓ 批量处理正常
✓ 错误处理正常
```

## 🛠️ 故障排除

### 常见问题

1. **ImportError: attempted relative import**
   - 解决方案：已修复，支持相对和绝对导入

2. **配置无效**
   - 检查 `default_config.py` 中的 API 密钥是否正确
   - 确保网络连接正常

3. **向量维度不匹配**
   - BGE-M3 模型输出 1024 维向量
   - OpenAI 模型通常输出 1536 或 3072 维向量

## 📁 文件结构

```
rag/
├── simple_vectorizer.py      # 简单向量化器
├── enhanced_vectorizer.py    # 增强版向量化器  
├── vectorizer_config.py      # 配置管理
├── vectorizer_example.py     # 使用示例
└── USAGE.md                  # 本使用指南
```

## 🎯 核心优势

1. **自动配置**: 从 `default_config.py` 自动获取配置
2. **灵活性**: 支持多种配置方式和模型
3. **错误处理**: 完善的错误处理和重试机制
4. **易用性**: 简洁的 API 设计
5. **兼容性**: 支持相对和绝对导入

## 📝 注意事项

- 确保 `default_config.py` 中的 API 密钥有效
- BGE-M3 模型适合中文文本处理
- 批处理大小已针对不同模型优化
- 支持 SiliconFlow 和 OpenAI 等多个 API 提供商
